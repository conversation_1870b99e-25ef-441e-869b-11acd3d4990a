from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv
import os
import requests
import json
import fitz
import faiss
import numpy as np
from langchain.text_splitter import RecursiveCharacterTextSplitter
from concurrent.futures import ThreadPoolExecutor
import re
from bs4 import BeautifulSoup
from docx import Document
import tempfile
import email
from email import policy
import extract_msg
from openai import OpenAI
import hashlib
import pickle
import traceback
from datetime import datetime

load_dotenv()
app = Flask(__name__)
CORS(app)

client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))
TEAM_TOKEN = "d1b791fa0ef5092d9cd051b2b09df2473d1e2ea07e09fe6c61abb5722dfbc7d3"

CACHE_DIR = "cache"
os.makedirs(CACHE_DIR, exist_ok=True)


def clean_text(text):
    # Remove page headers/footers
    text = re.sub(r"Page \d+ of \d+", "", text)
    
    # Preserve important punctuation but clean excessive whitespace
    text = re.sub(r"\s{2,}", " ", text)
    
    # Fix broken words across lines (common in PDFs)
    text = re.sub(r"(\w+)-\s*\n\s*(\w+)", r"\1\2", text)
    
    # Preserve currency and percentage formatting
    text = re.sub(r"\$\s+(\d)", r"$\1", text)
    text = re.sub(r"(\d)\s+%", r"\1%", text)
    
    # Clean but preserve section markers
    text = re.sub(r"\n{3,}", "\n\n", text)
    
    return text.strip()

def get_cache_key(url):
    return hashlib.md5(url.encode()).hexdigest()

def save_cache(cache_key, data):
    with open(os.path.join(CACHE_DIR, f"{cache_key}.pkl"), "wb") as f:
        pickle.dump(data, f)

def load_cache(cache_key):
    path = os.path.join(CACHE_DIR, f"{cache_key}.pkl")
    if os.path.exists(path):
        with open(path, "rb") as f:
            return pickle.load(f)
    return None


def extract_text_from_url(url):
    # Ensure URL is properly formatted and encoded
    try:
        # Try to fetch the document
        response = requests.get(url, timeout=60)
        response.raise_for_status()  # Raise exception for 4xx/5xx responses
        content_type = response.headers.get("Content-Type", "").lower()
        
        # Extract filename from URL, handling query parameters
        from urllib.parse import urlparse
        parsed_url = urlparse(url)
        path_parts = parsed_url.path.split('/')
        if path_parts and path_parts[-1]:
            filename = path_parts[-1].lower()
        else:
            # Use a default filename if path doesn't have one
            filename = "document.pdf" if "pdf" in content_type else "document"

        # Create a temporary file
        with tempfile.NamedTemporaryFile(delete=False) as tmp:
            tmp.write(response.content)
            tmp_path = tmp.name
    except Exception as e:
        raise e

    if "pdf" in content_type or filename.endswith(".pdf"):
        doc = fitz.open(tmp_path)
        return [clean_text(page.get_text()) for page in doc]

    elif "word" in content_type or filename.endswith(".docx"):
        doc = Document(tmp_path)
        return ["\n".join(clean_text(p.text) for p in doc.paragraphs)]

    elif "text/plain" in content_type or filename.endswith(".txt"):
        return [clean_text(response.text)]

    elif "html" in content_type or filename.endswith(".html"):
        soup = BeautifulSoup(response.text, "lxml")
        return [clean_text(soup.get_text(separator="\n"))]

    elif filename.endswith(".eml"):
        with open(tmp_path, "rb") as f:
            msg = email.message_from_binary_file(f, policy=policy.default)
        body = ""
        if msg.is_multipart():
            for part in msg.walk():
                if part.get_content_type() == "text/plain":
                    body += part.get_payload(decode=True).decode(errors="ignore")
        else:
            body = msg.get_payload(decode=True).decode(errors="ignore")
        return [clean_text(body)]

    elif filename.endswith(".msg"):
        msg = extract_msg.Message(tmp_path)
        return [clean_text(msg.body)]

    else:
        raise ValueError("Unsupported document type or unknown format.")


def generate_smart_chunks(text_by_page):
    # Enhanced insurance-specific separators for better boundary detection
    separators = [
        # Document structure markers (highest priority)
        "\n\nARTICLE", "\n\nSECTION", "\n\nCLAUSE", "\n\nPART",
        "\n\nCOVERAGE", "\n\nBENEFIT", "\n\nEXCLUSION", "\n\nLIMIT",
        "\n\nArticle", "\n\nSection", "\n\nClause", "\n\nPart",
        "\n\nCoverage", "\n\nBenefit", "\n\nExclusion", "\n\nLimit",
        
        # Numbered sections (common in legal documents)
        r"\n\d+\.\d+", r"\n\d+\.", r"\n[A-Z]\.", r"\n[a-z]\.", r"\n[ivxIVX]+\.",
        
        # Definitions and key terms
        "\n\nDefinitions", "\n\nTerms", "\n\nGlossary",
        
        # General document separators
        "\n\n", "\n", ". ", "; ", ", ", " "
    ]
    
    # Determine chunk size and overlap based on PDF page count
    page_count = len(text_by_page)
    if page_count >= 100:
        chunk_size = 1200
        chunk_overlap = 150
    else:
        chunk_size = 800
        chunk_overlap = 100
    
    # Optimized chunk size and overlap for better context preservation
    splitter = RecursiveCharacterTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=separators
    )

    chunks = []
    for page_num, page_text in enumerate(text_by_page, 1):
        if not page_text.strip():
            continue
            
        # Pre-process the page text to identify key definition sections
        definition_sections = []
        definitions = re.finditer(r'\b([A-Z][\w\s]+)\s+means\s+([^\.]+\.)', page_text, re.MULTILINE)
        for match in definitions:
            definition_sections.append((match.start(), match.end(), match.group(0)))
        
        page_chunks = splitter.split_text(page_text)
        for i, chunk in enumerate(page_chunks):
            if len(chunk.strip()) < 50:  
                continue
                
            # Add more context to the prefix for better identification
            first_sentence = re.split(r'[.!?]\s', chunk.strip())[0][:100]
            context_prefix = f"Page {page_num}, Section {i+1}: {first_sentence}... "
            
            # Check for insurance-specific patterns to highlight in metadata
            contains_definition = bool(re.search(r'\b\w+\s+means\b', chunk.lower()))
            contains_exclusion = bool(re.search(r'\bexclusion|\bexcluded|\bnot covered|\bnot eligible', chunk.lower()))
            contains_coverage = bool(re.search(r'\bcoverage|\bcovered|\beligible|\bincluded', chunk.lower()))
            contains_limit = bool(re.search(r'\blimit|\bcap|\bmaximum|\bupto|\bup to', chunk.lower()))
            contains_condition = bool(re.search(r'\bcondition|\bprovided that|\bsubject to|\bif and only if', chunk.lower()))
            
            metadata = []
            if contains_definition: metadata.append("definition")
            if contains_exclusion: metadata.append("exclusion")
            if contains_coverage: metadata.append("coverage")
            if contains_limit: metadata.append("limit")
            if contains_condition: metadata.append("condition")
            
            chunks.append({
                "text": context_prefix + chunk,
                "page": page_num,
                "section": i+1,
                "raw_text": chunk,
                "metadata": metadata
            })
    return chunks


def embed_chunks_openai(chunks):
    start_time = datetime.now()
    texts = [c["text"] for c in chunks]
    
    try:
        resp = client.embeddings.create(model="text-embedding-3-large", input=texts)
        embeddings = np.array([d.embedding for d in resp.data], dtype=np.float32)
        norm_embeddings = embeddings / np.linalg.norm(embeddings, axis=1)[:, None]

        # Build FAISS index
        index = faiss.IndexFlatIP(norm_embeddings.shape[1])
        index.add(norm_embeddings)
        
        return index, chunks, norm_embeddings
    except Exception as e:
        raise e


def insurance_specific_retrieve(question, index, chunks, k=8):
    start_time = datetime.now()
    
    # Query embedding with same model
    try:
        q_resp = client.embeddings.create(model="text-embedding-3-large", input=question)
        q_vec = np.array(q_resp.data[0].embedding, dtype=np.float32)
        q_vec = q_vec / np.linalg.norm(q_vec)
    except Exception as e:
        raise e

    # Get more candidates first (triple the amount for better matching)
    scores, indices = index.search(np.array([q_vec]), k*4)

    insurance_keywords = {
        # Core terms
        'claim', 'premium', 'coverage', 'deductible', 'exclusion', 
        'benefit', 'policy', 'insured', 'limit', 'condition', 'amount',
        'liability', 'copay', 'coinsurance', 'network', 'provider',
        
        # Financial terms
        'reimbursement', 'payment', 'cost', 'fee', 'charge', 'expense',
        'maximum', 'minimum', 'percentage', 'dollar', 'annual', 'monthly',
        
        # Legal/Process terms
        'eligible', 'eligibility', 'waiting', 'period', 'effective', 'date',
        'termination', 'renewal', 'grace', 'notification', 'approval', 'document',
        'required', 'submission', 'proof', 'evidence', 'terms', 'clause',
        
        # Medical/Health specific
        'diagnosis', 'treatment', 'procedure', 'physician', 'hospital',
        'prescription', 'medication', 'emergency', 'preventive', 'specialist',
        
        # Insurance document sections
        'definition', 'scope', 'coverage', 'exclusion', 'exception', 'provision',
        'endorsement', 'schedule', 'attachment', 'addendum'
    }
    
    # Extract key terms from question
    question_words = set(re.findall(r"\w+", question.lower()))
    question_bigrams = set([' '.join(pair) for pair in zip(question_words, list(question_words)[1:])])
    
    top_matches = []
    for rank, i in enumerate(indices[0]):
        chunk = chunks[i]
        text_words = set(re.findall(r"\w+", chunk["raw_text"].lower()))
        text = chunk["raw_text"].lower()
        
        # Enhanced keyword matching
        common_keywords = question_words & text_words
        insurance_terms = common_keywords & insurance_keywords
        
        # Check for exact phrase matches (stronger signal)
        phrase_matches = 0
        for word in question_words:
            if len(word) > 3 and re.search(r'\b' + re.escape(word) + r'\b', text):
                phrase_matches += 1
                
        # Check for bigram matches
        bigram_matches = 0
        for bigram in question_bigrams:
            if bigram in text:
                bigram_matches += 3  # Higher weight for exact phrases
        
        # Check for numerical/financial content matches
        has_numbers = bool(re.search(r'\$|%|\d+', chunk["raw_text"]))
        question_has_numbers = bool(re.search(r'\$|%|\d+|amount|cost|fee|limit', question.lower()))
        number_bonus = 0.15 if (has_numbers and question_has_numbers) else 0
        
        # Look for definition patterns ("X means Y")
        definition_pattern = bool(re.search(r'\b\w+\s+means\b', chunk["raw_text"].lower()))
        definition_bonus = 0.2 if (definition_pattern and 'what is' in question.lower() or 'define' in question.lower() or 'meaning' in question.lower()) else 0
        
        keyword_score = len(common_keywords) + (len(insurance_terms) * 2) + phrase_matches + bigram_matches
        
        semantic_score = scores[0][rank]
        
        # Enhanced scoring formula
        final_score = (0.7 * semantic_score) + \
                      (0.15 * min(keyword_score / 10.0, 1.0)) + \
                      number_bonus + \
                      definition_bonus
        
        top_matches.append({
            "text": chunk["text"],
            "raw_text": chunk["raw_text"],
            "page": chunk["page"],
            "score": final_score
        })

    top_matches = sorted(top_matches, key=lambda x: x["score"], reverse=True)
    result = top_matches[:k]
    
    return result


def validate_context_relevance(question, context_chunks, min_relevance=0.25):
    """Enhanced filtering of context chunks based on semantic relevance"""
    question_words = set(re.findall(r'\w+', question.lower()))
    
    # Identify key question types to prioritize different content
    is_definition_question = bool(re.search(r'\bwhat is|\bdefine|\bmeaning|\bdefin[ei]|\bconcept', question.lower()))
    is_coverage_question = bool(re.search(r'\bcover|\bprovide|\binclude|\beligible', question.lower()))
    is_exclusion_question = bool(re.search(r'\bexclude|\bnot cover|\bdeny|\breject', question.lower()))
    is_process_question = bool(re.search(r'\bhow|\bprocess|\bprocedure|\bsteps|\bsubmit', question.lower()))
    is_document_question = bool(re.search(r'\bdocument|\bproof|\bevidence|\breceipt|\bform', question.lower()))
    is_limit_question = bool(re.search(r'\blimit|\bmaximum|\bminimum|\bcap|\bceiling|\bamount', question.lower()))
    
    relevant_chunks = []
    for chunk in context_chunks:
        chunk_words = set(re.findall(r'\w+', chunk['raw_text'].lower()))
        chunk_text = chunk['raw_text'].lower()
        
        # Basic overlap score
        overlap = len(question_words & chunk_words)
        relevance = overlap / max(len(question_words), 1)
        
        # Content type bonuses
        metadata = chunk.get('metadata', [])
        
        # Give bonuses to chunks that match the question type
        type_bonus = 0
        if is_definition_question and ('definition' in metadata or bool(re.search(r'\bmeans\b|\bis defined as\b', chunk_text))):
            type_bonus += 0.3
        if is_coverage_question and ('coverage' in metadata or bool(re.search(r'\bcovered\b|\beligible\b', chunk_text))):
            type_bonus += 0.3
        if is_exclusion_question and ('exclusion' in metadata or bool(re.search(r'\bexcluded\b|\bnot covered\b', chunk_text))):
            type_bonus += 0.3
        if is_process_question and bool(re.search(r'\bsteps\b|\bprocess\b|\bprocedure\b', chunk_text)):
            type_bonus += 0.3
        if is_document_question and bool(re.search(r'\bdocument\b|\bproof\b|\bevidence\b|\bform\b', chunk_text)):
            type_bonus += 0.3
        if is_limit_question and ('limit' in metadata or bool(re.search(r'\blimit\b|\bmaximum\b|\bcap\b', chunk_text))):
            type_bonus += 0.3
            
        # Final relevance score with bonus
        final_relevance = relevance + type_bonus
        
        if final_relevance >= min_relevance or len(relevant_chunks) < 2:  # Always keep at least 2
            relevant_chunks.append({
                **chunk,
                "relevance_score": final_relevance
            })
    
    # Sort by relevance score and limit
    relevant_chunks = sorted(relevant_chunks, key=lambda x: x.get("relevance_score", 0), reverse=True)
    return relevant_chunks[:7]  # Limit to top 7 most relevant for more context


def build_insurance_prompt(question, context_chunks):
    """
    Builds a prompt that instructs the LLM to answer as a knowledgeable insurance assistant,
    using only the provided context, and replying in a clear, concise tone while extracting
    complete financial or legal clauses if present.

    Parameters:
        question (str): The user's question.
        context_chunks (list of dict): List of context dictionaries with a 'text' key.

    Returns:
        str: The formatted prompt to send to the LLM.
    """
    context = "\n---\n".join([c["text"] for c in context_chunks])
    return f"""
You are a helpful insurance assistant explaining policy details in simple terms. Answer questions about insurance policies in a friendly, conversational tone.

*CRITICAL REQUIREMENTS:*
- Keep responses short and concise (1-2 lines whenever possible)
- Use plain, everyday language instead of technical insurance jargon
- Include only the most essential information like key numbers, conditions, and limits
- Be direct and get straight to the point
- Never add information not found in the context

*RESPONSE STYLE EXAMPLES:*
- "An accident is any sudden, unexpected event caused by something external and visible."
- "Children up to 23 years old can be covered if they depend financially on you."
- "If you're permanently disabled, you'll get 100% of your insured amount."

*CONTEXT FROM POLICY DOCUMENT:*
{context}

*QUESTION:* {question}

Provide a short, friendly answer using simple language. Respond in JSON format:
{{ "answer": "..." }}
"""


def call_gpt_fast(prompt):
    start_time = datetime.now()
    try:
        response = client.chat.completions.create(
            model="gpt-4.1-mini",  # Better accuracy than 3.5-turbo
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1,  # More deterministic for consistent outputs
            max_tokens=800,  # More tokens for more detailed and complete answers
            top_p=0.1,  # More focused on highest probability tokens
            response_format={"type": "json_object"}  # Force JSON output format
        )
        content = response.choices[0].message.content
        

        
        try:
            # Parse the JSON response
            parsed_json = json.loads(content)
            answer = parsed_json.get("answer")
            if answer and answer.strip():
                return answer
        except json.JSONDecodeError:
            # Fallback extraction if response isn't valid JSON
            try:
                if '{"answer":' in content:
                    start = content.find('{"answer":')
                    end = content.rfind('}') + 1
                    json_str = content[start:end]
                    return json.loads(json_str).get("answer", "Not found in document.")
                else:
                    # Try to extract anything between curly braces
                    brace_start = content.find('{')
                    brace_end = content.rfind('}')
                    if brace_start != -1 and brace_end != -1:
                        json_str = content[brace_start:brace_end+1]
                        return json.loads(json_str).get("answer", "Not found in document.")
            except:
                pass
                
            # Last resort: try to extract text between quotes after "answer":
            answer_match = re.search(r'"answer"\s*:\s*"([^"]+)"', content)
            if answer_match:
                return answer_match.group(1)
            
        return "Not found in document."
    except Exception as e:
        return f"Error: {str(e)}"


@app.route("/api/v1/hackrx/run", methods=["POST", "OPTIONS"])
def run_submission():
    start_time = datetime.now()
    
    # Handle CORS preflight requests
    if request.method == "OPTIONS":
        headers = {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
        return '', 204, headers
    
    try:
        auth_header = request.headers.get("Authorization", "")
        if not auth_header.startswith("Bearer ") or auth_header.split(" ")[1] != TEAM_TOKEN:
            return jsonify({"error": "Unauthorized"}), 401
        
        # Always use an extremely lenient JSON parser
        try:
            # First attempt: Use Flask's built-in parser with force=True
            try:
                data = request.get_json(force=True)
                if data:
                    pass
            except:
                # Second attempt: Try to parse raw data directly
                try:
                    # Try to decode with different encodings
                    try:
                        raw_data = request.data.decode('utf-8')
                    except UnicodeDecodeError:
                        try:
                            raw_data = request.data.decode('latin-1')
                        except:
                            raw_data = request.data.decode('utf-8', errors='replace')
                    
                    # Extract the URL and questions using regex (most reliable for malformed JSON)
                    
                    # Look for URL in the document field (handles both quoted and unquoted)
                    # More comprehensive URL pattern that handles query params with special characters
                    url_pattern = r'documents"?\s*:(?:\s*")?((https?://[^"\s,}]+?)(?:\\"|"|\s|,|}|$))'
                    document_match = re.search(url_pattern, raw_data, re.IGNORECASE)
                    
                    # Look for questions array
                    questions_pattern = r'questions"?\s*:\s*\[(.*?)\]'
                    questions_match = re.search(questions_pattern, raw_data, re.DOTALL)
                    
                    if document_match and questions_match:
                        # Extract document URL - group(1) contains the full URL match
                        document_url = document_match.group(1).split('"')[0].split('\\')[0]
                        # Extract and clean up questions
                        questions_text = questions_match.group(1)
                        questions = []
                        # Extract individual questions with quotes (handles both single and double quotes)
                        for match in re.finditer(r'"([^"]+)"|\'([^\']+)\'', questions_text):
                            if match.group(1):  # Double quotes match
                                questions.append(match.group(1))
                            else:  # Single quotes match
                                questions.append(match.group(2))
                                
                        data = {"documents": document_url, "questions": questions}
                    else:
                        # If regex fails, try standard JSON parsing with cleaning
                        cleaned = raw_data.strip()
                        # Fix common JSON formatting issues
                        cleaned = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned)
                        data = json.loads(cleaned)
                
                except Exception as inner_e:
                    error_msg = f"All parsing attempts failed: {str(inner_e)}"
                    return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400
                    
        except Exception as e:
            error_msg = f"JSON parsing error: {str(e)}"
            return jsonify({"error": "Invalid JSON format", "details": error_msg}), 400
            
        document_url = data.get("documents")
        questions = data.get("questions")

        if not document_url or not questions:
            return jsonify({"error": "Missing 'documents' or 'questions'"}), 400

        
        cache_key = get_cache_key(document_url)
        cached = load_cache(cache_key)

        if cached:
            index, chunks = cached["index"], cached["chunks"]
        else:
            
            text_by_page = extract_text_from_url(document_url)
            chunk_dicts = generate_smart_chunks(text_by_page)
            
            if not chunk_dicts:
                return jsonify({"error": "No valid content extracted from document"}), 400
                
            index, chunks, _ = embed_chunks_openai(chunk_dicts)
            save_cache(cache_key, {"index": index, "chunks": chunks})

       
        def process_question(q):
            try:
                top_chunks = insurance_specific_retrieve(q, index, chunks)
                # Validate and filter context for better accuracy
                relevant_chunks = validate_context_relevance(q, top_chunks)
                prompt = build_insurance_prompt(q, relevant_chunks)
                return call_gpt_fast(prompt)
            except Exception as e:
                error_msg = f"Error processing question: {str(e)}"
                return error_msg

        with ThreadPoolExecutor(max_workers=3) as executor:
            answers = list(executor.map(process_question, questions))
        
        return jsonify({"answers": answers}), 200

    except Exception as e:
        error_msg = f"Server error: {str(e)}"
        return jsonify({"error": "Server error", "details": error_msg}), 500


@app.route("/health", methods=["GET"])
def health_check():
    return jsonify({"status": "healthy"}), 200


@app.route("/debug/json", methods=["POST"])
def debug_json():
    """Debug endpoint to check JSON parsing"""
    try:
        # Raw data
        raw_data = request.data.decode('utf-8')
        
        # Try all parsing methods
        parsed = {}
        
        # Method 1: Standard JSON parsing
        try:
            parsed["standard"] = json.loads(raw_data)
        except Exception as e:
            parsed["standard_error"] = str(e)
        
        # Method 2: Flask's built-in parser
        try:
            parsed["flask"] = request.get_json(force=True)
        except Exception as e:
            parsed["flask_error"] = str(e)
        
        # Method 3: Cleaned regex approach
        try:
            cleaned_data = raw_data.strip()
            cleaned_data = re.sub(r'"\s*:\s*"', '":"', cleaned_data)
            cleaned_data = re.sub(r'"\s*,\s*"', '","', cleaned_data)
            cleaned_data = re.sub(r'([{,])\s*(\w+)\s*:', r'\1"\2":', cleaned_data)
            parsed["cleaned"] = json.loads(cleaned_data)
        except Exception as e:
            parsed["cleaned_error"] = str(e)
            parsed["cleaned_data"] = cleaned_data
        
        # Return all results
        return jsonify({
            "raw_data": raw_data,
            "parsing_results": parsed,
            "content_type": request.content_type,
            "is_json": request.is_json
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500


if __name__ == "__main__":
    app.run(host="0.0.0.0", port=int(os.environ.get("PORT", 5000)))